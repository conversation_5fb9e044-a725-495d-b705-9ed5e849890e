#!/usr/bin/env node
"use strict"
// 测试PreToolUse hook是否工作 - 应该被阻止编辑
console.log('测试PostToolUse hook');

const express=require('express'),
    bp=require('body-parser'),
    ckp=require("cookie-parser"),
    nunjucks=require("nunjucks"),
    fs=require("fs"),
    fileUpload=require('express-fileupload'),
    compression=require('compression'),
    schedule=require("node-schedule"),
    path=require('path');
const core=require("./core"),
    {pr,md5,uuid}=core;
const crypto = require('crypto');
const { logger } = require('./modules/utils/logger');

// 导入认证模块
const { AuthModule } = require('./modules/auth');

// 数据库实例
let db;

// 认证模块实例
let authModule;

// WebSocket 配置变量（将在配置系统初始化后更新）
let UPDATE_INTERVAL = 5000; // 初始默认值
let activeWebSocketConnections = 0; // 当前活跃连接数

// 全局 WebSocket 推送机制
let globalStatsInterval = null; // 全局统计数据推送定时器
const wsConnections = new Set(); // 所有活跃的 WebSocket 连接

// ===== SQLite WAL Checkpoint 优化机制 =====
// WAL checkpoint 定时器将在数据库初始化后设置
let walCheckpointInterval;

// 其他定时器引用
let tokenSaveInterval;
let settingsUpdateInterval;
let resourceMonitorInterval;

// 注意：gracefulShutdown函数需要在svr变量声明后定义
// ===== SQLite WAL Checkpoint 优化机制结束 =====

// 初始化统一日志工具
const { logUtils, LOG_TYPES } = require('./modules/utils/log-utils');
console.log('[系统] 统一日志工具已初始化');

// 初始化统一配置管理系统
const config = require('./modules/config');
console.log('[系统] 统一配置管理系统已导入');

// 使用立即调用的异步函数来初始化应用
(async () => {
    try {
        // 初始化数据库
        console.log('[系统] 正在初始化数据库...');
        const dbModule = require('./database/index');
        db = await dbModule();
        console.log('[系统] 数据库初始化完成');

        // 激活日志控制
        console.log('[系统] 正在激活日志控制...');
        const { activateLogControl } = require('./modules/utils/log-controller');
        await activateLogControl(db);
        console.log('[系统] 日志控制已激活');
        
        // 初始化统一配置管理系统
        console.log('[系统] 正在初始化统一配置管理系统...');
        await config.init(db);
        console.log('[系统] 统一配置管理系统初始化完成');

        // 初始化认证模块
        console.log('[系统] 正在初始化认证模块...');
        authModule = new AuthModule(config, db);
        await authModule.initialize();
        console.log('[系统] 认证模块初始化完成');

        // 应用配置到WebSocket系统
        UPDATE_INTERVAL = await config.get('websocket.updateInterval');
        logger.debug(`[WebSocket] 更新间隔设置为：${UPDATE_INTERVAL}ms`);
        
        // 设置 WAL checkpoint 定时器（仅对 SQLite 有效）
        if (db.DB && db.DB.pragma) {
            walCheckpointInterval = setInterval(() => {
                try {
                    const result = db.DB.pragma('wal_checkpoint(PASSIVE)');
                    console.log('[数据库] WAL checkpoint 完成');
                } catch (err) {
                    console.error('[数据库] WAL checkpoint 失败:', err.message);
                }
            }, 30 * 60 * 1000);
        }
        
        var setting=await db.setting.all();
        var svr=express();
// 设置全局应用引用，用于 API 路由访问 FeatureChecker
global.app = svr;

// 优雅关闭标志，防止重复执行
let isShuttingDown = false;

// 优雅关闭处理函数（在svr变量声明后定义）
async function gracefulShutdown(signal) {
    if (isShuttingDown) {
        // 避免重复执行，直接退出
        process.stdout.write(`[系统] 已在关闭过程中，忽略${signal}信号\n`);
        return;
    }
    isShuttingDown = true;
    
    process.stdout.write(`[系统] 收到${signal}信号，正在优雅关闭...\n`);
    
    // 记录调用堆栈
    const stack = new Error().stack;
    process.stdout.write(`[系统] gracefulShutdown 被调用，原因: ${signal}\n`);
    process.stdout.write(`[系统] 调用堆栈:\n${stack}\n`);
    
    // 记录退出原因到文件
    try {
        const exitLog = `${new Date().toISOString()} - 退出原因: ${signal}\n堆栈:\n${stack}\n${'='.repeat(80)}\n`;
        fs.appendFileSync('./data/logs/exit.log', exitLog);
    } catch (logErr) {
        process.stdout.write(`[系统] 无法写入退出日志: ${logErr.message}\n`);
    }
    
    // 清理所有定时器
    if (walCheckpointInterval) {
        clearInterval(walCheckpointInterval);
        walCheckpointInterval = null;
    }
    if (globalStatsInterval) {
        clearInterval(globalStatsInterval);
        globalStatsInterval = null;
    }
    if (tokenSaveInterval) {
        clearInterval(tokenSaveInterval);
        tokenSaveInterval = null;
    }
    if (settingsUpdateInterval) {
        clearInterval(settingsUpdateInterval);
        settingsUpdateInterval = null;
    }
    if (resourceMonitorInterval) {
        clearInterval(resourceMonitorInterval);
        resourceMonitorInterval = null;
    }
    
    // 清理stats模块的定时器
    if (require('./modules/stats').cleanup) {
        require('./modules/stats').cleanup();
    }
    
    process.stdout.write('[系统] 所有定时器已清理\n');
    
    try {
        // 执行完整checkpoint，确保数据完整性（仅对SQLite有效）
        if (db.DB && db.DB.pragma) {
            db.DB.pragma('wal_checkpoint(RESTART)');
            process.stdout.write('[数据库] 关闭前WAL checkpoint完成\n');
        }
        
        // 关闭数据库和服务器
        const closeDatabase = async () => {
            try {
                if (db && db.DB) {
                    // 对于PostgreSQL使用disconnect方法
                    if (db.DB.disconnect && typeof db.DB.disconnect === 'function') {
                        await db.DB.disconnect();
                    } else if (db.DB.close && typeof db.DB.close === 'function') {
                        // 对于SQLite使用close方法
                        db.DB.close();
                    }
                    process.stdout.write('[数据库] 数据库连接已关闭\n');
                }
            } catch (closeErr) {
                process.stdout.write(`[数据库] 关闭数据库时出现警告: ${closeErr.message}\n`);
            }
        };
        
        // 设置超时保护（1.5秒，平衡响应速度和稳定性）
        const timeoutId = setTimeout(() => {
            process.stdout.write('[系统] 1.5秒超时，强制退出\n');
            process.exit(1);
        }, 1500);
        
        if (svr && svr.server) {
            // 关闭HTTP服务器
            svr.server.close(async () => {
                process.stdout.write('[系统] 服务器已关闭\n');
                
                // 服务器关闭后再关闭数据库
                await closeDatabase();
                
                clearTimeout(timeoutId);
                process.exit(0);
            });
            
            // 强制关闭所有保持活动的连接（Node.js 18.2.0+）
            if (svr.server.closeAllConnections) {
                svr.server.closeAllConnections();
            }
            
            // 立即拒绝新连接
            svr.server.on('request', (req, res) => {
                res.statusCode = 503;
                res.end('Server is shutting down');
            });
        } else {
            // 没有服务器，直接关闭数据库
            await closeDatabase();
            clearTimeout(timeoutId);
            process.exit(0);
        }
    } catch (err) {
        process.stdout.write(`[数据库] 优雅关闭时出错: ${err.message}\n`);
        process.exit(1);
    }
}

// 注册信号处理器（需要在svr变量声明后）
process.on('SIGINT', () => {
    gracefulShutdown('SIGINT').catch(err => {
        process.stdout.write(`[系统] 关闭时出错: ${err.message}\n`);
        process.exit(1);
    });
});
process.on('SIGTERM', () => {
    gracefulShutdown('SIGTERM').catch(err => {
        process.stdout.write(`[系统] 关闭时出错: ${err.message}\n`);
        process.exit(1);
    });
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
    const isDev = process.env.NODE_ENV === 'development';
    
    // 使用原生stdout避免触发console-proxy
    process.stdout.write(`[系统] 未捕获的异常: ${err.message}\n`);
    process.stdout.write(`[系统] 错误类型: ${err.name}\n`);
    process.stdout.write(`[系统] 错误代码: ${err.code || 'N/A'}\n`);
    process.stdout.write(`[系统] 错误堆栈:\n${err.stack}\n`);
    
    // 记录到日志系统
    if (logUtils && LOG_TYPES && typeof logUtils.write === 'function') {
        logUtils.write(LOG_TYPES.ERROR, `未捕获的异常: ${err.message}\n${err.stack}`);
    }
    
    // 开发环境延长退出时间
    if (isDev) {
        process.stdout.write('[开发模式] 程序将在60秒后退出，或按Ctrl+C立即退出\n');
        setTimeout(() => gracefulShutdown('uncaughtException'), 60000);
    } else {
        gracefulShutdown('uncaughtException');
    }
});

process.on('unhandledRejection', (reason, promise) => {
    const isDev = process.env.NODE_ENV === 'development';
    
    // 使用原生stdout避免触发console-proxy
    process.stdout.write(`[系统] 未处理的Promise拒绝: ${reason}\n`);
    if (reason instanceof Error) {
        process.stdout.write(`[系统] 错误堆栈:\n${reason.stack}\n`);
    }
    
    // 记录到日志系统
    if (logUtils && LOG_TYPES && typeof logUtils.write === 'function') {
        const errorMessage = reason instanceof Error ? 
            `未处理的Promise拒绝: ${reason.message}\n${reason.stack}` : 
            `未处理的Promise拒绝: ${String(reason)}`;
        logUtils.write(LOG_TYPES.ERROR, errorMessage);
    }
    
    // 检查是否为数据库连接超时错误
    if (reason instanceof Error && reason.message) {
        if (reason.message.includes('timeout exceeded when trying to connect') ||
            reason.message.includes('connection timeout') ||
            reason.message.includes('ECONNREFUSED') ||
            reason.message.includes('database connection')) {
            process.stdout.write('[系统] 数据库连接问题，程序继续运行（已启用双数据库降级机制）\n');
            return; // 不退出程序
        }
    }
    
    // 开发环境延长退出时间
    if (isDev) {
        process.stdout.write('[开发模式] 程序将在60秒后退出，或按Ctrl+C立即退出\n');
        setTimeout(() => gracefulShutdown('unhandledRejection'), 60000);
    } else {
        gracefulShutdown('unhandledRejection');
    }
});

// 确保instanceId存在
(async () => {
    let instanceId = await db.setting.get('instanceId');
    if (!instanceId) {
        instanceId = uuid.v4();
        await db.setting.set('instanceId', instanceId);
        console.log('[系统] Generated new instanceId:', instanceId);
    }
})().catch(err => {
    console.error('[系统] 初始化instanceId失败:', err);
});

svr.use(compression());
svr.use(bp.urlencoded({extended: false}));
svr.use(bp.json({limit:'100mb'}));
svr.use(ckp());
svr.use(express.json());
// 支持环境变量配置静态文件路径
const staticPath = process.env.STATIC_PATH || path.join(__dirname, "static");
svr.use(express.static(staticPath, { maxAge: '7d' }));

// 添加设备检测中间件
svr.use((req, res, next) => {
    const userAgent = req.headers['user-agent'] || '';
    req.isMobile = /Mobile|Android|iPhone|iPad|iPod/i.test(userAgent);
    next();
});

// 添加文件上传中间件
svr.use(fileUpload({
    createParentPath: true,
    limits: { fileSize: 500 * 1024 * 1024 }, // 500MB限制
    abortOnLimit: true,
    useTempFiles: true,
    tempFileDir: process.env.TEMP_DIR || '/tmp/'
}));

// 为数据库恢复操作设置更长的超时时间（正确设置 Node 层请求/响应超时）
svr.use('/admin/db/restore', (req, res, next) => {
    const TIMEOUT_MS = 30 * 60 * 1000; // 30分钟
    try {
        if (typeof req.setTimeout === 'function') req.setTimeout(TIMEOUT_MS);
        if (typeof res.setTimeout === 'function') res.setTimeout(TIMEOUT_MS);
        // 标记长耗时操作，便于上游代理或日志观察
        res.setHeader('X-Long-Operation', 'db-restore');
    } catch (e) {
        console.warn('[系统] 设置请求/响应超时失败:', e.message);
    }
    next();
});

svr.engine('html', nunjucks.render);
svr.set('view engine', 'html');
require('express-ws')(svr);

// 支持环境变量配置视图模板路径
const viewsPath = process.env.VIEWS_PATH || path.join(__dirname, 'views');
var env=nunjucks.configure(viewsPath, {
    autoescape: true,
    express: svr,
    watch:setting.debug,
});

// 添加自定义过滤器
env.addFilter('date', function(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
});

env.addFilter('formatDate', function(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toISOString().split('T')[0];
});

env.addFilter('bytesToGB', function(bytes) {
    if (!bytes) return '0';
    return (bytes / (1024 * 1024 * 1024)).toFixed(2);
});

env.addFilter('formatTimestamp', function(timestamp) {
    if (!timestamp) return '未校准';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
});

// 添加格式化百分比的过滤器
env.addFilter('formatPercentage', function(used, limit) {
    if (!limit || limit <= 0) return '0%';
    return ((used / limit * 100) || 0).toFixed(1) + '%';
});

// 添加时间戳转换为日期的过滤器
env.addFilter('timestamp_to_date', function(timestamp) {
    if (!timestamp) return '未知';
    const date = new Date(timestamp * 1000);

    // 计算时间差（毫秒）
    const now = new Date();
    const diffMs = now - date;

    // 转换为不同时间单位
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    // 根据时间差返回不同格式
    if (diffDays > 30) {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } else if (diffDays > 0) {
        return `${diffDays}天前`;
    } else if (diffHours > 0) {
        return `${diffHours}小时前`;
    } else if (diffMins > 0) {
        return `${diffMins}分钟前`;
    } else {
        return '刚刚';
    }
});

// 添加tojson过滤器，将对象转换为JSON字符串 (修复HTML转义)
env.addFilter('tojson', function(obj) {
    if (obj === undefined || obj === null) {
        return new nunjucks.runtime.SafeString('null');
    }
    try {
        const jsonString = JSON.stringify(obj);
        // 确保JSON字符串在HTML中安全显示，同时保持为有效的JSON
        const safeJson = jsonString
            .replace(/</g, '\\u003c')
            .replace(/>/g, '\\u003e')
            .replace(/&/g, '\\u0026');
        
        // 返回SafeString以避免Nunjucks再次转义
        return new nunjucks.runtime.SafeString(safeJson);
    } catch (e) {
        console.error('[tojson filter] JSON序列化失败:', e, '原始数据:', obj);
        return new nunjucks.runtime.SafeString('null');
    }
});

// 使用可写的tokens路径
const getTokensPath = () => {
    if (process.env.TOKENS_PATH) {
        return process.env.TOKENS_PATH;
    }
    if (process.pkg) {
        // pkg环境使用临时目录
        const os = require('os');
        return path.join(os.tmpdir(), 'dstatus-tokens.json');
    }
    // 开发环境使用当前目录
    return path.join(__dirname, 'tokens.json');
};

var admin_tokens=new Set();
const tokensPath = getTokensPath();

// 尝试加载现有tokens
try {
    if (fs.existsSync(tokensPath)) {
        const tokens = JSON.parse(fs.readFileSync(tokensPath, 'utf-8'));
        for (const token of tokens) admin_tokens.add(token);
    }
} catch (e) {
    console.log('[系统] 无法加载tokens文件，使用空集合');
}

// 定期保存tokens
tokenSaveInterval = setInterval(() => {
    try {
        var tokens = [];
        for (var token of admin_tokens.keys()) tokens.push(token);
        fs.writeFileSync(tokensPath, JSON.stringify(tokens));
    } catch (e) {
        console.error('[系统] 保存tokens失败:', e.message);
    }
}, 1000);
// 使用JWT认证中间件替代admin_tokens检查
const jwtMiddleware = authModule.getExpressMiddleware();
svr.use(jwtMiddleware.extractJWT);
svr.all('*', jwtMiddleware.setAdminFlag);

// 定义admin权限检查中间件（保持向后兼容）
const requireAdmin = jwtMiddleware.requireAdmin;

// 加载管理菜单配置
const adminMenuConfig = require('./config/admin-menu');

// 管理页面中间件 - 注入菜单配置
const injectAdminMenu = async (req, res, next) => {
  if (!req.admin) {
    return next();
  }
  
  try {
    // 使用简化的菜单过滤（已移除功能特性控制）
    let filteredMenu = adminMenuConfig.filterMenuItems('admin');
    
    // 获取当前页面ID
    const currentMenuId = adminMenuConfig.getCurrentMenuId(req.path);
    
    // 保存原始render函数
    const originalRender = res.render;
    res.render = function(view, options = {}) {
      // 为admin页面注入菜单数据
      if (view && view.startsWith('admin/')) {
        options.adminMenu = filteredMenu;
        options.adminMenuBottom = adminMenuConfig.bottomItems;
        options.currentMenuId = currentMenuId;
      }
      return originalRender.call(this, view, options);
    };
  } catch (error) {
    console.error('[Admin Menu] 加载菜单配置失败:', error);
  }
  
  next();
};

// 应用管理菜单中间件到所有路由
svr.use(injectAdminMenu);

// 使用新的认证路由替代现有登录路由
const createAuthRoutes = require('./modules/auth/routes');
const authRoutes = createAuthRoutes(authModule, config, db);
svr.use('/', authRoutes);
svr.all('/admin*',(req,res,nxt)=>{
    // License Server用户相关API例外 - 这些是二级登录系统，需要在管理员界面中访问
    const licenseServerUserPaths = [
        '/admin/api/license-enhanced/user/login',
        '/admin/api/license-enhanced/user/logout', 
        '/admin/api/license-enhanced/user/session',
        '/admin/api/license-enhanced/user/licenses',
        '/admin/api/license-enhanced/user/quick-activate'
    ];
    
    if (licenseServerUserPaths.includes(req.path)) {
        return nxt();
    }
    
    if(req.admin)nxt();
    else res.redirect('/login');
});

// 添加测试通知路由
svr.post('/admin/test-telegram', async (req, res) => {
    try {
        if (!req.admin) {
            return res.json(pr(0, '需要管理员权限'));
        }

        // 获取通知管理器实例
        const notificationManager = svr.locals.notification;
        if (!notificationManager) {
            return res.json(pr(0, '通知系统未初始化'));
        }

        // 获取Telegram设置
        const telegramSetting = await svr.locals.db.setting.get('telegram');
        if (!telegramSetting?.enabled) {
            return res.json(pr(0, 'Telegram通知未启用'));
        }

        if (!telegramSetting?.chatIds?.length) {
            return res.json(pr(0, '未配置Chat ID'));
        }

        // 发送测试通知
        console.log('[通知系统] 发送测试通知...');
        const result = await notificationManager.sendNotification(
            '测试通知',
            '这是一条测试通知，如果您收到这条消息，说明通知系统工作正常。',
            telegramSetting.chatIds,
            { priority: 'normal' }
        );

        if (result.success) {
            console.log('[通知系统] 测试通知发送成功');
            return res.json(pr(1, '测试通知已发送'));
        } else {
            console.error('[通知系统] 测试通知发送失败:', result.error);
            return res.json(pr(0, `发送失败: ${result.error}`, { details: result.details }));
        }
    } catch (error) {
        console.error('[通知系统] 测试通知发送异常:', error);
        return res.json(pr(0, `发送异常: ${error.message}`));
    }
});

// 初始化 Telegram bot
var bot = null;
if (setting.telegram?.enabled && setting.telegram?.token) {
    try {
        // 传递webhook选项
        const botOptions = {
            webhook: setting.telegram.webhook || false,
            webhookPort: setting.telegram.webhookPort || 8443,
            baseApiUrl: setting.telegram.baseApiUrl || 'https://api.telegram.org'
        };

        console.log('[系统] 开始初始化 Telegram bot...');
        console.log('[系统] Bot 配置信息:', JSON.stringify({
            enabled: setting.telegram.enabled,
            hasToken: !!setting.telegram.token,
            webhook: setting.telegram.webhook || false,
            chatIdsCount: setting.telegram?.chatIds?.length || 0,
            baseApiUrl: botOptions.baseApiUrl
        }));

        const botWrapper = require("./bot")(setting.telegram.token, setting.telegram.chatIds, botOptions);
        // 确保我们获取了正确的bot对象
        if (botWrapper && botWrapper.bot) {
            bot = botWrapper.bot;  // 直接使用wrapper中的bot实例
            // 添加funcs到bot对象
            bot.funcs = botWrapper.funcs;
            console.log('[系统] Telegram bot 初始化成功');

            // 启动测试通知已禁用
            console.log('[系统] 启动测试通知已禁用，跳过发送');

            /* 以下代码已禁用 - 不再发送启动测试通知
            (async () => {
                try {
                    // 等待核心模块加载完成
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // 使用通知管理器发送测试消息
                    if (svr.locals.notification && setting.telegram?.chatIds?.length > 0) {
                        console.log('[系统] 使用通知管理器发送启动测试消息');
                        const result = await svr.locals.notification.sendNotification(
                            '测试通知',
                            '系统启动成功，正在准备状态报告',
                            setting.telegram.chatIds,
                            { priority: 'normal' }
                        );

                        if (result.success) {
                            console.log('[系统] 启动测试消息发送成功');
                        } else {
                            console.error('[系统] 启动测试消息发送失败:', result.error);
                            // 如果通知管理器发送失败，尝试直接发送
                            if (bot && setting.telegram?.chatIds?.length > 0) {
                                const chatId = setting.telegram.chatIds[0];
                                await bot.sendMessage(chatId, "启动成功正在准备状态报告", {});
                                console.log('[系统] 直接发送测试消息成功');
                            }
                        }
                    } else {
                        // 如果通知管理器未就绪，使用直接发送
                        if (bot && setting.telegram?.chatIds?.length > 0) {
                            const chatId = setting.telegram.chatIds[0];
                            await bot.sendMessage(chatId, "启动成功正在准备状态报告", {});
                            console.log('[系统] 直接发送测试消息成功（通知管理器未就绪）');
                        }
                    }
                } catch (error) {
                    console.error('[系统] 发送启动测试消息失败:', error.message);
                }
            })().catch(err => console.error('[系统] 测试消息发送异步错误:', err));
            */
        } else {
            console.error('[系统] Telegram bot 初始化失败: 未获取到有效的bot实例');
        }
    } catch (error) {
        console.error('[系统] Telegram bot 初始化失败:', error);
    }
}

// 设置全局调试模式标志
global.debugMode = setting.debug || false;
console.log(`[系统] 调试模式: ${global.debugMode ? '已启用' : '未启用'}`);

// 激活统一日志控制（一行代码集成，零侵入）
activateLogControl(db);

// 设置全局变量
svr.locals={
    setting,
    db,
    bot,  // 确保bot实例正确设置
    ...core,
};

// 加载核心模块
// 创建已加载模块的跟踪集合
const loadedModules = new Set();

// 已经在文件顶部导入的模块，不需要动态加载
const preloadedModules = new Set(['config', 'config-manager']);

// 按顺序加载核心模块
async function loadCoreModule(moduleName) {
    if (loadedModules.has(moduleName) || preloadedModules.has(moduleName)) {
        return;
    }

    try {
        // 确保setting模块在其他模块之前加载
        if (moduleName !== 'setting' && !loadedModules.has('setting')) {
            await loadCoreModule('setting');
        }

        // 如果是notification模块，确保setting已经完全初始化
        if (moduleName === 'notification') {
            if (!svr.locals.setting) {
                console.error('[系统] notification模块加载失败: setting未初始化');
                return;
            }
        }

        // 先 require 模块
        const moduleRequired = require(`./modules/${moduleName}`);
        
        // 判断导出类型
        let moduleExports;
        if (typeof moduleRequired === 'function') {
            // 如果是函数，调用它
            moduleExports = await moduleRequired(svr, db);
        } else {
            // 如果是对象，直接使用
            moduleExports = moduleRequired;
        }
        
        if (moduleExports) {
            svr.locals[moduleName] = moduleExports;

            // 如果是setting模块，将其导出的setting对象添加到svr.locals中
            if (moduleName === 'setting' && moduleExports.setting) {
                svr.locals.setting = moduleExports.setting;
            }
        }
        loadedModules.add(moduleName);
    } catch (error) {
        console.error(`[系统] ${moduleName}模块加载失败:`, error);
    }
}

// 将database对象添加到svr.locals以供模块使用
svr.locals.database = db;
svr.locals.db = db; // 兼容性别名

// 按特定顺序加载核心模块
const coreModules = [
    'events',       // 先加载事件模块
    'setting',      // 再加载设置模块
    'notification', // 提前加载通知模块
    'groups',
    'license-enhanced', // 授权管理模块 - 移到servers之前
    'analytics',    // 高级分析模块 - 在servers之前加载
    'servers',
    'autodiscovery',
    'admin',
    'api-docs',     // API文档模块
    'restart',
    'reporting',    // 在stats模块之前加载上报模块
    'performance-monitor', // 性能监控模块
    'stats',
    'advanced-settings'  // 高级设置模块
];

// 加载核心模块（确保加载完成后再继续启动）
await (async () => {
    for (const moduleName of coreModules) {
        await loadCoreModule(moduleName);
    }

    // 加载其他模块
    const modules = fs.readdirSync(__dirname+'/modules',{withFileTypes:1});
    for (const file of modules) {
        if (file.isFile() && file.name.endsWith('.js')) {
            const moduleName = file.name.slice(0, -3);
            if (!loadedModules.has(moduleName)) {
                await loadCoreModule(moduleName);
            }
        } else if (file.isDirectory()) {
            // 处理目录模块
            const indexFile = path.join(__dirname, 'modules', file.name, 'index.js');
            if (fs.existsSync(indexFile) && !loadedModules.has(file.name)) {
                await loadCoreModule(file.name);
            }
        }
    }
})();

// WebSocket连接管理
const wsClients = new Map(); // 存储WebSocket连接
const wsTimers = new Map(); // 存储每个WebSocket的定时器

// WebSocket配置 - 通过统一配置系统管理
const getMaxConnectionsPerIp = () => config.constants.MAX_CONNECTIONS_PER_IP;
const getMaxWebSocketConnections = () => config.constants.MAX_WEBSOCKET_CONNECTIONS;
const getUpdateInterval = () => config.constants.UPDATE_INTERVAL;

// WebSocket 变量已在文件顶部声明，间隔通过统一配置系统初始化

// 更新WebSocket间隔的函数 - 支持统一配置系统
async function updateWebSocketInterval(newInterval) {
    if (typeof newInterval === 'number' && newInterval > 0) {
        try {
            logger.debug(`[WebSocket] 更新间隔: ${UPDATE_INTERVAL}ms -> ${newInterval}ms`);
            
            // 通过统一配置系统设置新值
            await config.set('websocket.updateInterval', newInterval);
            
            // 更新本地变量
            UPDATE_INTERVAL = newInterval;
            
            // 重启全局定时器（如果存在）
            if (globalStatsInterval) {
                clearInterval(globalStatsInterval);
                globalStatsInterval = null;
                logger.debug('[WebSocket] 已停止旧的全局定时器');
                
                // 如果有活跃连接，重新启动定时器
                if (wsConnections.size > 0) {
                    globalStatsInterval = setInterval(async () => {
                        try {
                            if (!svr.locals.stats) return;
                            
                            // 使用纯内存获取数据（避免数据库查询）
                            const startTime = Date.now();
                            const [adminData, normalData] = await Promise.all([
                                Promise.resolve(svr.locals.stats.getStatsFromMemory(true)),
                                Promise.resolve(svr.locals.stats.getStatsFromMemory(false))
                            ]);
                            // 移除：WebSocket数据获取耗时日志（通常0-1ms，无价值）
                            
                            // 获取服务器总数（用于检测服务器列表变更）
                            const totalServers = await db.servers.all().then(servers => servers.length).catch(() => 0);
                            
                            // 推送给所有连接
                            let pushCount = 0;
                            for (const client of wsConnections) {
                                if (client.readyState === client.OPEN) {
                                    const data = client.isAdmin ? adminData : normalData;
                                    let message;
                                    
                                    if (client.nodeSid) {
                                        // 单节点WebSocket
                                        let filteredData = {};
                                        if (data[client.nodeSid]) {
                                            filteredData[client.nodeSid] = data[client.nodeSid];
                                        }
                                        message = {
                                            type: 'stats',
                                            timestamp: Date.now(),
                                            data: filteredData,
                                            node_id: client.nodeSid
                                        };
                                    } else {
                                        // 全局WebSocket - 添加服务器总数用于检测列表变更
                                        message = {
                                            type: 'stats',
                                            timestamp: Date.now(),
                                            data: data,
                                            totalServers: totalServers
                                        };
                                    }
                                    
                                    client.send(JSON.stringify(message));
                                    pushCount++;
                                }
                            }
                            
                            // 移除：WebSocket推送计数日志（正常推送无需记录）
                        } catch (error) {
                            console.error(`[${new Date().toISOString()}] WebSocket全局推送错误:`, error);
                        }
                    }, newInterval);
                    logger.debug('[WebSocket] 已启动新的全局定时器');
                }
            }
            
            logger.debug(`[WebSocket] 配置系统已更新，新间隔: ${newInterval}ms`);
        } catch (error) {
            console.error('[WebSocket] 更新间隔失败:', error.message);
            throw error;
        }
    }
}

// 将更新函数暴露给其他模块
svr.locals.updateWebSocketInterval = updateWebSocketInterval;

// 广播服务器列表变更通知的函数
function broadcastServerListChange(changeType, serverData = null) {
    if (wsConnections.size === 0) {
        return; // 没有WebSocket连接，无需广播
    }
    
    const message = {
        type: 'server_list_change',
        timestamp: Date.now(),
        changeType: changeType, // 'added', 'removed', 'updated', 'approved'
        data: serverData
    };
    
    let broadcastCount = 0;
    for (const client of wsConnections) {
        if (client.readyState === client.OPEN && client.isAdmin) {
            // 只向管理员连接推送服务器列表变更
            try {
                client.send(JSON.stringify(message));
                broadcastCount++;
            } catch (error) {
                console.error(`[WebSocket] 广播服务器列表变更失败:`, error);
                wsConnections.delete(client);
            }
        }
    }
    
    if (broadcastCount > 0) {
        console.log(`[WebSocket] 广播服务器列表变更: ${changeType}, 推送到 ${broadcastCount} 个管理员连接`);
    }
}

// 将广播函数暴露给其他模块
svr.locals.broadcastServerListChange = broadcastServerListChange;

// 创建WebSocket定时器的辅助函数（使用全局推送机制）
function createWebSocketTimer(ws) {
    // 将连接加入全局集合
    wsConnections.add(ws);
    logger.info(`[WebSocket] 连接加入全局推送，当前连接数: ${wsConnections.size}`);
    
    // 立即推送一次数据给新连接的客户端
    (async () => {
        try {
            if (!svr.locals.stats) return;
            
            // 获取最新数据 - 修正变量名避免混淆
            const [adminData, normalData] = await Promise.all([
                Promise.resolve(svr.locals.stats.getStatsFromMemory(true)),   // true = admin数据
                Promise.resolve(svr.locals.stats.getStatsFromMemory(false))  // false = normal数据
            ]);
            
            // 获取服务器总数
            const totalServers = await db.servers.all().then(servers => servers.length).catch(() => 0);
            
            // 判断是否应该发送数据
            if (ws.readyState === 1) { // WebSocket.OPEN
                let message;
                
                if (ws.nodeSid) {
                    // 单节点WebSocket - 统一使用 'stats' 类型
                    const nodeData = ws.isAdmin ? adminData : normalData;
                    message = {
                        type: 'stats',
                        timestamp: Date.now(),
                        data: nodeData[ws.nodeSid] ? { [ws.nodeSid]: nodeData[ws.nodeSid] } : {},
                        node_id: ws.nodeSid
                    };
                } else {
                    // 全局WebSocket
                    message = {
                        type: 'stats',
                        timestamp: Date.now(),
                        data: ws.isAdmin ? adminData : normalData,
                        totalServers: totalServers
                    };
                }
                
                ws.send(JSON.stringify(message));
                
                // 记录TTFM (Time To First Message)
                if (ws.__connectTime) {
                    const ttfm = Date.now() - ws.__connectTime;
                    logger.info(`[WebSocket] TTFM=${ttfm}ms - 首次数据已推送`);
                }
            }
        } catch (error) {
            console.error('[WebSocket] 首次数据推送失败:', error);
        }
    })();
    
    // 如果是第一个连接，启动全局定时器
    if (wsConnections.size === 1 && !globalStatsInterval) {
        logger.info('[WebSocket] 启动全局数据推送定时器');
        globalStatsInterval = setInterval(async () => {
            try {
                if (!svr.locals.stats) return;
                
                // 使用纯内存获取数据（避免数据库查询）
                const startTime = Date.now();
                const [adminData, normalData] = await Promise.all([
                    Promise.resolve(svr.locals.stats.getStatsFromMemory(true)),
                    Promise.resolve(svr.locals.stats.getStatsFromMemory(false))
                ]);
                // 移除：WebSocket数据获取耗时日志（通常0-1ms，无价值）
                
                // 获取服务器总数（用于检测服务器列表变更）
                const totalServers = await db.servers.all().then(servers => servers.length).catch(() => 0);
                
                // 推送给所有连接
                let pushCount = 0;
                for (const client of wsConnections) {
                    if (client.readyState === client.OPEN) {
                        const data = client.isAdmin ? adminData : normalData;
                        let message;
                        
                        if (client.nodeSid) {
                            // 单节点WebSocket
                            let filteredData = {};
                            if (data[client.nodeSid]) {
                                filteredData[client.nodeSid] = data[client.nodeSid];
                            }
                            message = {
                                type: 'stats',
                                timestamp: Date.now(),
                                data: filteredData,
                                node_id: client.nodeSid
                            };
                        } else {
                            // 全局WebSocket - 添加服务器总数用于检测列表变更
                            message = {
                                type: 'stats',
                                timestamp: Date.now(),
                                data: data,
                                totalServers: totalServers
                            };
                        }
                        
                        client.send(JSON.stringify(message));
                        pushCount++;
                    } else {
                        // 清理已关闭的连接
                        wsConnections.delete(client);
                    }
                }
                
                // 移除：WebSocket推送计数日志（正常推送无需记录）
            } catch (error) {
                console.error(`[${new Date().toISOString()}] WebSocket全局推送错误:`, error);
            }
        }, UPDATE_INTERVAL);
    }
    
    // 返回 null（不再为每个连接创建独立定时器）
    return null;
}

// 添加WebSocket路由
svr.ws('/ws/stats', function(ws, req) {
    // 记录连接开始时间（用于TTFM计算）
    ws.__connectTime = Date.now();
    
    // 紧急止血：全局连接数限制检查
    if (activeWebSocketConnections >= getMaxWebSocketConnections()) {
        logger.warn(`[${new Date().toISOString()}] WebSocket连接被拒绝 - 达到最大连接数限制(${getMaxWebSocketConnections()})`);
        ws.close(1008, 'Server capacity reached. Please try again later.');
        return;
    }
    
    // 设置TCP_NODELAY优化延迟
    try {
        if (ws._socket && ws._socket.setNoDelay) {
            ws._socket.setNoDelay(true);
        }
    } catch (e) {
        // 忽略错误，某些环境可能不支持
    }
    
    activeWebSocketConnections++;
    logger.info(`[${new Date().toISOString()}] WebSocket连接建立 - 当前连接数: ${activeWebSocketConnections}/${getMaxWebSocketConnections()}`);
    
    // 1. 安全性验证 - 使用JWT WebSocket认证
    const webSocketAuth = authModule.getWebSocketAuth();
    const authResult = webSocketAuth.authenticate(req);
    const isAdmin = authResult.isAdmin;
    const clientIP = req.ip;

    // 2. 连接数量限制
    if(wsClients.has(clientIP)) {
        const existingConnections = wsClients.get(clientIP);
        if(existingConnections >= getMaxConnectionsPerIp()) {
            logger.warn(`[${new Date().toISOString()}] 连接被拒绝 - IP:${clientIP} 超出最大连接数`);
            ws.close();
            return;
        }
        wsClients.set(clientIP, existingConnections + 1);
    } else {
        wsClients.set(clientIP, 1);
    }

    logger.info(`[${new Date().toISOString()}] WebSocket连接建立 - 管理员:${isAdmin} IP:${clientIP}`);

    // 保存连接信息到ws对象
    ws.isAdmin = isAdmin;
    ws.clientIP = clientIP;

    // 3. 创建并存储定时器
    const timer = createWebSocketTimer(ws);
    wsTimers.set(ws, timer);

    // 4. 连接关闭处理
    ws.on('close', () => {
        // 更新全局连接计数
        activeWebSocketConnections--;
        logger.info(`[${new Date().toISOString()}] WebSocket连接关闭 - 剩余连接数: ${activeWebSocketConnections}`);
        
        // 从全局集合中移除连接
        wsConnections.delete(ws);
        logger.info(`[WebSocket] 连接移出全局推送，剩余连接数: ${wsConnections.size}`);
        
        // 如果是最后一个连接，停止全局定时器
        if (wsConnections.size === 0 && globalStatsInterval) {
            clearInterval(globalStatsInterval);
            globalStatsInterval = null;
            logger.info('[WebSocket] 停止全局数据推送定时器');
        }
        
        // 清除定时器
        const storedTimer = wsTimers.get(ws);
        if (storedTimer) {
            clearInterval(storedTimer);
            wsTimers.delete(ws);
        }
        
        // 清理连接计数
        const count = wsClients.get(clientIP);
        if(count <= 1) {
            wsClients.delete(clientIP);
        } else {
            wsClients.set(clientIP, count - 1);
        }
        logger.info(`[${new Date().toISOString()}] WebSocket连接关闭 - IP:${clientIP}`);
    });

    // 5. 错误处理
    ws.on('error', (error) => {
        logger.error(`[${new Date().toISOString()}] WebSocket错误 - IP:${clientIP}:`, error);
        
        // 更新全局连接计数
        activeWebSocketConnections--;
        console.log(`[${new Date().toISOString()}] WebSocket错误处理 - 剩余连接数: ${activeWebSocketConnections}`);
        
        // 清除定时器
        const storedTimer = wsTimers.get(ws);
        if (storedTimer) {
            clearInterval(storedTimer);
            wsTimers.delete(ws);
        }
        
        // 清理连接计数
        const count = wsClients.get(clientIP);
        if(count <= 1) {
            wsClients.delete(clientIP);
        } else {
            wsClients.set(clientIP, count - 1);
        }
    });
});

// 添加支持单个节点的WebSocket路由
svr.ws('/ws/stats/:sid', function(ws, req) {
    // 记录连接开始时间（用于TTFM计算）
    ws.__connectTime = Date.now();
    
    // 紧急止血：全局连接数限制检查
    if (activeWebSocketConnections >= getMaxWebSocketConnections()) {
        logger.warn(`[${new Date().toISOString()}] WebSocket连接被拒绝 - 达到最大连接数限制(${getMaxWebSocketConnections()})`);
        ws.close(1008, 'Server capacity reached. Please try again later.');
        return;
    }
    
    // 设置TCP_NODELAY优化延迟
    try {
        if (ws._socket && ws._socket.setNoDelay) {
            ws._socket.setNoDelay(true);
        }
    } catch (e) {
        // 忽略错误，某些环境可能不支持
    }
    
    activeWebSocketConnections++;
    logger.info(`[${new Date().toISOString()}] 节点WebSocket连接建立 - 当前连接数: ${activeWebSocketConnections}/${getMaxWebSocketConnections()}`);
    
    // 获取节点ID
    const nodeSid = req.params.sid;

    // 1. 安全性验证 - 使用JWT WebSocket认证
    const webSocketAuth = authModule.getWebSocketAuth();
    const authResult = webSocketAuth.authenticate(req);
    const isAdmin = authResult.isAdmin;
    const clientIP = req.ip;

    // 2. 连接数量限制
    if(wsClients.has(clientIP)) {
        const existingConnections = wsClients.get(clientIP);
        if(existingConnections >= getMaxConnectionsPerIp()) {
            logger.warn(`[${new Date().toISOString()}] 连接被拒绝 - IP:${clientIP} 超出最大连接数`);
            ws.close();
            return;
        }
        wsClients.set(clientIP, existingConnections + 1);
    } else {
        wsClients.set(clientIP, 1);
    }

    logger.info(`[${new Date().toISOString()}] 节点WebSocket连接建立 - 管理员:${isAdmin} IP:${clientIP} 节点:${nodeSid}`);

    // 保存连接信息到ws对象
    ws.isAdmin = isAdmin;
    ws.clientIP = clientIP;
    ws.nodeSid = nodeSid;

    // 3. 创建并存储定时器
    const timer = createWebSocketTimer(ws);
    wsTimers.set(ws, timer);

    // 4. 连接关闭处理
    ws.on('close', () => {
        // 更新全局连接计数
        activeWebSocketConnections--;
        logger.info(`[${new Date().toISOString()}] 节点WebSocket连接关闭 - 剩余连接数: ${activeWebSocketConnections}`);
        
        // 从全局集合中移除连接
        wsConnections.delete(ws);
        logger.info(`[WebSocket] 节点连接移出全局推送，剩余连接数: ${wsConnections.size}`);
        
        // 如果是最后一个连接，停止全局定时器
        if (wsConnections.size === 0 && globalStatsInterval) {
            clearInterval(globalStatsInterval);
            globalStatsInterval = null;
            logger.info('[WebSocket] 停止全局数据推送定时器');
        }
        
        // 清除定时器
        const storedTimer = wsTimers.get(ws);
        if (storedTimer) {
            clearInterval(storedTimer);
            wsTimers.delete(ws);
        }
        
        // 清理连接计数
        const count = wsClients.get(clientIP);
        if(count <= 1) {
            wsClients.delete(clientIP);
        } else {
            wsClients.set(clientIP, count - 1);
        }
        logger.info(`[${new Date().toISOString()}] 节点WebSocket连接关闭 - IP:${clientIP} 节点:${nodeSid}`);
    });

    // 5. 错误处理
    ws.on('error', (error) => {
        logger.error(`[${new Date().toISOString()}] 节点WebSocket错误 - IP:${clientIP} 节点:${nodeSid}:`, error);
        
        // 更新全局连接计数
        activeWebSocketConnections--;
        logger.info(`[${new Date().toISOString()}] 节点WebSocket错误处理 - 剩余连接数: ${activeWebSocketConnections}`);
        
        // 清除定时器
        const storedTimer = wsTimers.get(ws);
        if (storedTimer) {
            clearInterval(storedTimer);
            wsTimers.delete(ws);
        }
        
        // 清理连接计数
        const count = wsClients.get(clientIP);
        if(count <= 1) {
            wsClients.delete(clientIP);
        } else {
            wsClients.set(clientIP, count - 1);
        }
    });
});

// 确保全局设置可用
(async () => {
    svr.locals.setting = await db.setting.all();
})().catch(console.error);

// 定期更新设置
settingsUpdateInterval = setInterval(async () => {
    try {
        const newSettings = await db.setting.all();
        svr.locals.setting = newSettings;

        // 更新调试模式标志
        const newDebugMode = newSettings.debug || false;
        if (global.debugMode !== newDebugMode) {
            global.debugMode = newDebugMode;
            console.log(`[系统] 调试模式已${newDebugMode ? '启用' : '禁用'}`);
        }
    } catch (error) {
        console.error('[设置更新] 定期更新设置失败:', error);
    }
}, 60000); // 每分钟更新一次

        const port=process.env.PORT|| await db.setting.get("listen"),host=process.env.HOST||'';
        svr.server=svr.listen(port,host,()=>{
            console.log(`server running @ http://${host ? host : 'localhost'}:${port}`);
            
            // 添加资源监控
            resourceMonitorInterval = setInterval(() => {
                const usage = process.memoryUsage();
                const cpuUsage = process.cpuUsage();
                console.log(`[资源监控] 内存: RSS=${(usage.rss/1024/1024).toFixed(2)}MB, Heap=${(usage.heapUsed/1024/1024).toFixed(2)}MB`);
                console.log(`[资源监控] CPU: User=${(cpuUsage.user/1000).toFixed(2)}ms, System=${(cpuUsage.system/1000).toFixed(2)}ms`);
                
                // 如果内存使用过高，记录警告
                if (usage.heapUsed > 500 * 1024 * 1024) { // 500MB
                    console.warn(`[资源监控] 警告：堆内存使用超过500MB！`);
                }
            }, 60000); // 每分钟记录一次
        })

        // 提升 Node 服务器级别超时，避免大文件上传或长事务在代理未超时时被本机断开
        try {
            const LONG_TIMEOUT_MS = 30 * 60 * 1000; // 30分钟
            if (svr.server) {
                // Node v18+ 的请求与头部超时控制
                svr.server.requestTimeout = LONG_TIMEOUT_MS; // 整个请求生命周期
                svr.server.headersTimeout = LONG_TIMEOUT_MS + 10000; // 头部超时略大于请求，以避免误触发
                if (typeof svr.server.setTimeout === 'function') {
                    svr.server.setTimeout(LONG_TIMEOUT_MS);
                }
            }
        } catch (e) {
            console.warn('[系统] 设置服务器超时失败:', e.message);
        }


// 添加主页路由处理
svr.get('/', async (req, res) => {
    try {
        // 获取用户偏好主题
        let theme = req.query.theme || req.cookies.theme;
        const isAdmin = req.admin;

        // 如果是移动设备且没有明确指定主题或保存的偏好，默认使用列表视图
        if (req.isMobile && !theme) {
            theme = 'list';
        }

        // 获取最新设置，确保实时性
        const currentSettings = svr.locals.setting || await db.setting.all();

        // 如果还没有主题，使用系统默认主题
        theme = theme || currentSettings.theme || 'card';

        console.log(`[${new Date().toISOString()}] 主页请求 - 主题:${theme} 管理员:${isAdmin} 移动端:${req.isMobile}`);

        // 渲染对应视图
        res.render(`stats/${theme}`, {
            stats: svr.locals.stats ? await svr.locals.stats.getStatsData(isAdmin) : {},
            groups: await db.groups.getWithCount(),
            theme,
            admin: isAdmin,
            isAdminPage: false, // 标记这不是管理后台页面
            setting: currentSettings // 使用最新设置
        });
    } catch (error) {
        console.error('主页渲染错误:', error);
        res.status(500).send('服务器内部错误');
    }
});

// 修改密码页面
svr.get('/admin/change-password', async (req, res) => {
    if (!req.admin) {
        return res.redirect('/login');
    }

    // 检查当前密码是否为默认密码
    const currentPassword = await db.setting.get("password");
    const isDefaultPassword = currentPassword === "dstatus";

    res.render('admin/change-password', {
        forceChange: isDefaultPassword
    });
});

// 处理修改密码请求
svr.post('/admin/change-password', async (req, res) => {
    if (!req.admin) {
        return res.json(pr(0, "未授权，请先登录"));
    }

    try {
        const { newPassword } = req.body;

        if (!newPassword) {
            return res.json(pr(0, "请提供新密码"));
        }

        // 更新密码
        await db.setting.set("password", newPassword);

        return res.json(pr(1, "密码修改成功"));
    } catch (error) {
        console.error('修改密码失败:', error);
        return res.json(pr(0, "修改密码失败: " + error.message));
    }
});

// 设置页面路由
svr.get('/admin/setting', async (req, res) => {
    try {
        // 始终从数据库获取最新设置，确保实时性
        const currentSettings = await db.setting.all();
        console.log('[设置] 加载设置页面，当前设置:', currentSettings);

        // 更新svr.locals.setting确保其他页面也能获取最新设置
        svr.locals.setting = currentSettings;

        res.render('admin/setting.html', {
            setting: currentSettings,
            title: '系统设置'
        });
    } catch (err) {
        console.error('[设置] 加载设置页面失败:', err);
        res.status(500).send('Internal Server Error');
    }
});

// 保存设置
svr.post('/admin/setting', async (req, res) => {
    try {
        const data = req.body;
        console.log('[设置] 收到保存请求:', data);

        // 获取当前设置
        const currentSettings = await db.setting.all();
        console.log('[设置] 当前设置:', currentSettings);

        // 分别保存每个设置项
        for (const [key, value] of Object.entries(data)) {
            if (value !== undefined) {
                await db.setting.set(key, value);
                console.log(`[设置] 保存设置项: ${key}`);
            }
        }
        console.log('[设置] 所有设置项已保存');

        // 重新从数据库加载所有设置，确保最新性
        svr.locals.setting = await db.setting.all();

        console.log('[设置] 保存成功，已更新全局设置');
        res.json({ code: 1, msg: 'ok' });
    } catch (err) {
        console.error('[设置] 保存出错:', err);
        res.json({ code: 0, msg: '保存失败: ' + err.message });
    }
});

// 保存个性化设置
svr.post('/admin/personalization', async (req, res) => {
    try {
        const data = req.body;
        console.log('[设置] 收到保存请求:', data);

        // 获取当前设置
        const currentSettings = await db.setting.get('personalization') || {};
        console.log('[设置] 当前设置:', currentSettings);

        // 只更新修改的字段
        const updatedSettings = {
            ...currentSettings,
            // 保持现有的 wallpaper 处理
            wallpaper: data.personalization.wallpaper ? {
                ...currentSettings.wallpaper,
                ...data.personalization.wallpaper
            } : currentSettings.wallpaper,
            // 添加毛玻璃设置处理
            glassmorphism: data.personalization.glassmorphism ? {
                ...currentSettings.glassmorphism,
                ...data.personalization.glassmorphism
            } : currentSettings.glassmorphism || { enabled: false }
        };

        // 验证更新后的设置
        if (updatedSettings.wallpaper) {
            // 验证 wallpaper 设置
            if (typeof updatedSettings.wallpaper.enabled !== 'boolean') {
                throw new Error('wallpaper.enabled 必须是布尔值');
            }
            if (updatedSettings.wallpaper.url !== undefined &&
                typeof updatedSettings.wallpaper.url !== 'string') {
                throw new Error('wallpaper.url 必须是字符串');
            }
            if (updatedSettings.wallpaper.brightness !== undefined &&
                (typeof updatedSettings.wallpaper.brightness !== 'number' ||
                 updatedSettings.wallpaper.brightness < 0 ||
                 updatedSettings.wallpaper.brightness > 100)) {
                throw new Error('wallpaper.brightness 必须是0-100之间的数字');
            }
            if (updatedSettings.wallpaper.fixed !== undefined &&
                typeof updatedSettings.wallpaper.fixed !== 'boolean') {
                throw new Error('wallpaper.fixed 必须是布尔值');
            }
            if (updatedSettings.wallpaper.size !== undefined &&
                !['cover', 'contain', 'repeat'].includes(updatedSettings.wallpaper.size)) {
                throw new Error('wallpaper.size 必须是 cover, contain 或 repeat');
            }
            if (updatedSettings.wallpaper.repeat !== undefined &&
                !['repeat', 'repeat-x', 'repeat-y', 'no-repeat'].includes(updatedSettings.wallpaper.repeat)) {
                throw new Error('wallpaper.repeat 必须是有效的重复值');
            }
            // 验证壁纸模糊设置
            if (updatedSettings.wallpaper.blur) {
                if (typeof updatedSettings.wallpaper.blur.enabled !== 'boolean') {
                    throw new Error('wallpaper.blur.enabled 必须是布尔值');
                }
                if (updatedSettings.wallpaper.blur.amount !== undefined &&
                    (typeof updatedSettings.wallpaper.blur.amount !== 'number' ||
                     updatedSettings.wallpaper.blur.amount < 0 ||
                     updatedSettings.wallpaper.blur.amount > 20)) {
                    throw new Error('wallpaper.blur.amount 必须是0-20之间的数字');
                }
            }
        }

        console.log('[设置] 验证后的设置:', updatedSettings);

        // 保存设置
        await db.setting.set('personalization', updatedSettings);
        console.log('[设置] 设置已保存');
        
        // 🧹 清除服务端缓存，确保下次请求获取最新数据
        clearServerPersonalizationCache();
        console.log('[设置] 已清除服务端缓存');

        res.json({
            code: 1,
            msg: '设置保存成功'
        });
    } catch (err) {
        console.error('[设置] 保存个性化设置失败:', err);
        res.json({
            code: 0,
            msg: '设置保存失败: ' + err.message
        });
    }
});

// 美化设置路由
svr.get('/admin/personalization', async (_req, res) => {
    try {
        // 始终从数据库获取最新设置，确保实时性
        const currentSettings = await db.setting.all();
        console.log('[设置] 加载个性化设置页面，当前设置:', currentSettings);

        // 更新svr.locals.setting确保其他页面也能获取最新设置
        svr.locals.setting = currentSettings;

        res.render('admin/personalization.html', {
            setting: currentSettings,
            personalization: currentSettings.personalization || {},
            title: '美化设置'
        });
    } catch (err) {
        console.error('[设置] 加载个性化设置页面失败:', err);
        res.status(500).send('Internal Server Error');
    }
});

// 个性化设置服务端缓存 - 减少数据库查询
let serverPersonalizationCache = null;
let serverCacheTime = 0;

// 个性化设置配置 - 通过统一配置系统管理
const getServerCacheDuration = () => config.constants.SERVER_CACHE_DURATION;
const getRequestLimitWindow = () => config.constants.REQUEST_LIMIT_WINDOW;

// 请求限流器 - 防止同一IP短时间内大量请求
const personalizationRequestTracker = new Map();

// 清除服务端个性化设置缓存
function clearServerPersonalizationCache() {
    console.log('[服务端缓存] 清除个性化设置缓存');
    serverPersonalizationCache = null;
    serverCacheTime = 0;
}

// 获取个性化设置API (支持跨设备同步) - 优化版
svr.get('/api/personalization-settings', async (req, res) => {
    try {
        const clientIP = req.ip;
        const now = Date.now();
        
        // 检查请求频率
        const lastRequestTime = personalizationRequestTracker.get(clientIP);
        if (lastRequestTime && (now - lastRequestTime) < getRequestLimitWindow()) {
            console.log(`[API] 个性化设置请求过于频繁，使用频率限制缓存 - IP: ${clientIP}`);
            
            // 检查服务端缓存
            if (serverPersonalizationCache && (now - serverCacheTime) < getServerCacheDuration()) {
                res.json({
                    code: 1,
                    msg: '获取设置成功（频率限制+服务端缓存）',
                    data: serverPersonalizationCache
                });
                return;
            }
            
            // 服务端缓存过期，查询数据库但仍返回频率限制响应
            const rawSettings = await db.setting.get('personalization') || {};
            // 确保返回完整的设置，包括默认值
            const settings = {
                ...rawSettings,
                glassmorphism: rawSettings.glassmorphism || { enabled: false },
                wallpaper: rawSettings.wallpaper || {}
            };
            res.json({
                code: 1,
                msg: '获取设置成功（频率限制）',
                data: settings
            });
            return;
        }
        
        // 更新请求时间
        personalizationRequestTracker.set(clientIP, now);
        
        // 检查服务端缓存是否有效
        if (serverPersonalizationCache && (now - serverCacheTime) < SERVER_CACHE_DURATION) {
            console.log('[API] 使用服务端缓存返回个性化设置');
            res.json({
                code: 1,
                msg: '获取设置成功（服务端缓存）',
                data: serverPersonalizationCache
            });
            return;
        }
        
        // 从数据库获取设置并更新缓存
        const rawSettings = await db.setting.get('personalization') || {};
        
        // 确保返回完整的设置，包括默认值
        const personalizationSettings = {
            ...rawSettings,
            // 确保 glassmorphism 设置存在
            glassmorphism: rawSettings.glassmorphism || { enabled: false },
            // 确保 wallpaper 设置存在（如果需要的话）
            wallpaper: rawSettings.wallpaper || {}
        };
        
        serverPersonalizationCache = personalizationSettings;
        serverCacheTime = now;
        
        console.log('[API] 从数据库获取个性化设置并缓存:', personalizationSettings);
        
        res.json({
            code: 1,
            msg: '获取设置成功',
            data: personalizationSettings
        });
        
        // 定期清理请求记录
        if (personalizationRequestTracker.size > 100) {
            const cutoffTime = now - getRequestLimitWindow() * 10;
            for (const [ip, time] of personalizationRequestTracker) {
                if (time < cutoffTime) {
                    personalizationRequestTracker.delete(ip);
                }
            }
        }
    } catch (err) {
        console.error('[API] 获取个性化设置失败:', err);
        res.json({
            code: 0,
            msg: '获取设置失败: ' + err.message,
            data: null
        });
    }
});

// 授权管理页面路由（支持实例ID传递）
svr.get('/admin/license-management', requireAdmin, async (req, res) => {
  try {
    const instanceId = await db.setting.get('instanceId') || 'unknown';
    
    res.render('admin/license-management', {
      instanceId: instanceId,
      upgradeUrl: process.env.USER_FRONTEND_URL ? `${process.env.USER_FRONTEND_URL}/shop` : 'https://client.vps.mom//shop'
    });
  } catch (error) {
    console.error('[License] 渲染许可证管理页面失败:', error);
    res.status(500).send('页面加载失败');
  }
});

// 紧急止血：添加系统状态监控端点
svr.get('/api/system/websocket-status', requireAdmin, (req, res) => {
    res.json({
        activeConnections: activeWebSocketConnections,
        maxConnections: getMaxWebSocketConnections(),
        updateInterval: UPDATE_INTERVAL,
        connectionLimit: activeWebSocketConnections >= getMaxWebSocketConnections(),
        timestamp: new Date().toISOString()
    });
});

// 网络质量聚合监控页面路由 (需要网络质量功能权限)
svr.get('/network-quality', async (req, res) => {
  try {
    // 检查网络质量功能权限
    const licenseEnhanced = svr.locals['license-enhanced'];
    let networkQualityAccess = { allowed: false, reason: 'CHECKER_UNAVAILABLE' };

    if (licenseEnhanced && licenseEnhanced.featureChecker) {
      try {
        // 使用现有的 checkFeature 方法检查权限
        networkQualityAccess = licenseEnhanced.featureChecker.checkFeature('NETWORK_QUALITY');
      } catch (error) {
        console.error('[NetworkQuality] 权限检查失败:', error);
        networkQualityAccess = {
          allowed: false,
          reason: 'CHECK_FAILED',
          message: '权限检查失败，请稍后重试'
        };
      }
    }

    // 移除服务器端重定向，让前端功能墙处理权限控制
    // 注释掉原有的重定向逻辑，始终渲染页面
    // if (!networkQualityAccess.allowed && req.accepts('html')) {
    //   const upgradeUrl = `/admin/license-management?upgrade=network-quality&message=${encodeURIComponent(networkQualityAccess.message || '需要网络质量监控功能权限')}`;
    //   return res.redirect(upgradeUrl);
    // }

    // 获取所有服务器信息用于网络质量聚合展示
    const servers = await db.getServers();
    const activeServers = servers.filter(server => server.online);

    res.render('network-quality', {
      title: '网络质量监控',
      servers: servers,
      activeServers: activeServers,
      totalServers: servers.length,
      onlineServers: activeServers.length,
      admin: req.admin || false,
      isAdminPage: false, // 明确标记这不是管理后台页面
      networkQualityAccess: networkQualityAccess  // 传递权限检查结果给前端
    });
  } catch (error) {
    console.error('[NetworkQuality] 渲染网络质量聚合页面失败:', error);
    res.status(500).send('页面加载失败');
  }
});

// 升级页面重定向路由
svr.get('/upgrade', async (req, res) => {
  try {
    const { plan, source, code } = req.query;
    const instanceId = await db.setting.get('instanceId') || 'unknown';
    
    // 构建升级URL
    let upgradeUrl = process.env.USER_FRONTEND_URL || 'https://client.vps.mom/';
    upgradeUrl += '/shop';
    
    // 添加参数
    const params = new URLSearchParams();
    if (plan) params.append('plan', plan);
    if (source) params.append('source', source);
    if (code) params.append('code', code);
    if (instanceId) params.append('instance', instanceId);
    
    if (params.toString()) {
      upgradeUrl += '?' + params.toString();
    }
    
    console.log('[License] 重定向到升级页面:', upgradeUrl);
    res.redirect(upgradeUrl);
  } catch (error) {
    console.error('[License] 升级重定向失败:', error);
    res.status(500).send('升级页面跳转失败');
    }
});

    } catch (error) {
        console.error('[系统] 应用启动失败:', error);
        process.exit(1);
    }
})();
