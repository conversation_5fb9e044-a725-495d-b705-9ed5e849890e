# 管理员安全增强功能实施任务

## 任务概述

本任务列表将管理员安全增强功能分解为可执行的编码任务，按照依赖关系排序，确保每个任务都能基于前序任务的成果进行实现。

## 实施任务

- [x] 1. 创建数据库迁移和表结构
  - 创建admin_users、audit_logs、login_attempts、rate_limits、refresh_tokens表
  - 适配SQLite和PostgreSQL的数据类型和索引差异
  - 编写迁移脚本并集成到现有迁移系统
  - _需求: 8.1_

- [x] 2. 实现JWT管理器核心组件
  - 创建JWTManager类，支持Access+Refresh双令牌机制
  - 实现令牌签发、验证、刷新和撤销功能
  - 集成RefreshTokenStore进行令牌状态管理
  - 编写JWT管理器的单元测试
  - _需求: 1.1, 1.2_

- [x] 3. 实现2FA验证器组件
  - 创建TwoFactorAuth类，支持TOTP生成和验证
  - 实现TOTP密钥的AES-256-GCM加密存储
  - 实现备份码生成、验证和管理功能
  - 实现GitHub账号白名单检查机制
  - 编写2FA验证器的单元测试
  - _需求: 2.1, 2.2, 2.5_

- [x] 4. 创建认证中间件和路由
  - 实现JWT认证中间件替代admin_tokens检查
  - 创建动态登录路由系统
  - 实现登录、登出、2FA验证的API端点
  - 修改WebSocket认证机制支持JWT Bearer验证
  - _需求: 1.2, 1.4_

- [ ] 5. 实现登录入口管理器
  - 创建LoginPathManager类管理动态登录路径
  - 实现登录入口随机化和更新功能
  - 实现"发送最新入口"功能和IP限速
  - 集成通知系统发送入口变更通知
  - _需求: 3.1, 3.2, 3.4_

- [ ] 6. 实现限速和安全防护机制
  - 创建RateLimiter类管理各类限速策略
  - 实现登录失败锁定和渐进式惩罚机制
  - 实现IP和用户维度的限速控制
  - 集成审计日志记录所有安全事件
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [ ] 7. 集成通知系统和事件处理
  - 扩展NotificationManager支持新的安全通知类型
  - 实现登录事件通知的限频和去抖机制
  - 实现通知内容的敏感信息脱敏
  - 配置邮件模板支持安全通知
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 8. 实现审计日志系统
  - 创建AuditLogger类记录所有安全相关事件
  - 实现审计日志的查询和分页功能
  - 确保敏感信息不被记录到日志中
  - 实现审计日志的定期清理机制
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 9. 集成统一配置系统
  - 将安全配置项添加到config/global-config.js
  - 实现配置验证和默认值设置
  - 确保配置变更能够实时生效
  - 实现JWT密钥的自动生成和安全存储
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 10. 创建管理界面
  - 实现安全设置页面（启用/禁用2FA、查看备份码）
  - 实现登录入口管理页面（显示当前入口、一键旋转）
  - 实现安全日志查看页面（登录事件和审计记录）
  - 修改/login页面为提示页面，添加"发送最新入口"按钮
  - _需求: 8.2, 8.3, 8.4, 8.5_

- [ ] 11. 实现数据迁移脚本
  - 创建从现有密码配置到admin用户的迁移脚本
  - 实现JWT密钥生成和初始化配置
  - 清理admin_tokens相关代码和tokens.json文件
  - 实现配置项的初始化和默认值设置
  - _需求: 8.1_

- [ ] 12. 编写集成测试
  - 实现完整登录流程的集成测试
  - 实现2FA验证流程的集成测试
  - 实现WebSocket JWT认证的集成测试
  - 实现通知系统安全事件的集成测试
  - _需求: 所有需求的集成验证_

- [ ] 13. 实现安全基线和生产配置
  - 配置HTTPS强制和安全Cookie属性
  - 实现生产环境的安全配置检查
  - 添加安全监控和告警机制
  - 实现密钥轮换和安全维护功能
  - _需求: 7.5, 安全基线要求_

- [ ] 14. 创建部署和验收脚本
  - 实现初始化仪表盘和系统自检功能
  - 创建迁移验收脚本验证所有功能正常
  - 实现系统健康检查和监控功能
  - 编写部署文档和操作指南
  - _需求: 附录F的验收标准_

- [ ] 15. 最终集成和清理
  - 移除所有admin_tokens相关的旧代码
  - 确保guest用户功能完全不受影响
  - 进行全面的安全测试和性能测试
  - 完成文档更新和代码审查
  - _需求: 9.1, 9.2, 9.3, 9.4, 9.5_